<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import moment from 'moment'
import { getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// 方式1：通过 globalProperties
const instance = getCurrentInstance()

const getWaittingModal = (type: any, url?: string) => {
  console.log('[Page] Calling show modal')
  instance?.proxy?.$showWaitingModal?.(type, url)
}

const zonename = ref('Asia/Shanghai')
const nowDate = ref(moment().format('YYYY-MM-DD HH:mm:ss'))
const zoneArray = ref([])

const formateTime = (time: string) => {
  console.log('time', time)
  try {
    // Handle the format: "Fri Aug 15 16:17:25 CTS 2025"
    // First, remove the timezone abbreviation (CTS) as moment.js doesn't recognize it
    const cleanTime = time.replace(/\s+[A-Z]{3}\s+/, ' ')

    // Parse with the specific format: "ddd MMM DD HH:mm:ss YYYY"
    const parsedTime = moment(cleanTime, 'ddd MMM DD HH:mm:ss YYYY')

    if (parsedTime.isValid()) {
      nowDate.value = parsedTime.format('YYYY-MM-DD HH:mm:ss')
    }
    else {
      // Fallback: try to parse as-is
      const fallbackTime = moment(time)
      if (fallbackTime.isValid()) {
        nowDate.value = fallbackTime.format('YYYY-MM-DD HH:mm:ss')
      }
      else {
        console.error('Unable to parse time:', time)

        // Keep the current time if parsing fails
        nowDate.value = moment().format('YYYY-MM-DD HH:mm:ss')
      }
    }
  }
  catch (error) {
    console.error('Error parsing time:', error)
    nowDate.value = moment().format('YYYY-MM-DD HH:mm:ss')
  }
}

const getZoneNameArray = () => {
  $api('', {
    requestType: 207,
  }).then(res => {
    if (res.err_code == 0) {
      nowDate.value = moment(res.info.nowtime).format('YYYY-MM-DD HH:mm:ss')

      const zoneNameArray = res.info.zonename_array

      formateTime(res.info.date)

      zoneArray.value = zoneNameArray.map((item: any) => {
        return {
          label: item,
          value: item,
        }
      })
      zonename.value = res.info.zonename
    }
  })
}

// 106 修改时间 newTime
// 112 修改时区 newzonetime

const syncBrowserTime = () => {
  const newTime = moment().format('YYYY-MM-DD HH:mm:ss')

  $api('', {
    requestType: 106,
    data: { newTime },
  }).then(res => {
    if (res.err_code == 0) {
      ElMessage.success(t('SystemConfig.System.BrowserTimeSyncSuccess'))
      nowDate.value = newTime
    }
  })
}

const saveTimezone = () => {
  if (!zonename.value) {
    ElMessage.error(t('SystemConfig.System.SelectTimezone'))

    return
  }
  $api('', {
    requestType: 112,
    data: { newzonetime: zonename.value },
  }).then(res => {
    if (res.err_code == 0)
      ElMessage.success(t('SystemConfig.System.TimezoneSetSuccess'))
  })
}

onMounted(() => {
  getZoneNameArray()
})
</script>

<template>
  <VCard class="mb-5">
    <VCardText>
      <div class="flexBox">
        <div class="label">
          {{ t('SystemConfig.System.CurrentSystemTime') }}：
        </div>
        <div class="content">
          {{ nowDate }}
        </div>
        <div class="flex-1" />
        <VBtn
          variant="outlined"
          class="mr-12px"
          @click="syncBrowserTime"
        >
          {{ t('SystemConfig.System.SyncBrowserTime') }}
        </VBtn>
        <!--
          <VBtn variant="outlined">
          {{ t('SystemConfig.System.SyncNTPServerTime') }}
          </VBtn>
        -->
      </div>
    </VCardText>
  </VCard>
  <VCard class="mb-5">
    <VCardText>
      <VRow class="match-height mb-2">
        <!--
          <VCol cols="12" md="6">
          <div class="text-h6 mb-1">主机名称</div>
          <AppTextField v-model="LanInfo.lanIpAddress" placeholder="请输入主机名称" />
          </VCol>
        -->
        <VCol
          cols="12"
          md="6"
        >
          <div class="text-h6 mb-1">
            {{ t('SystemConfig.System.Timezone') }}
          </div>
          <AppSelect
            v-model="zonename"
            :items="zoneArray"
            item-title="label"
            :placeholder="t('SystemConfig.System.SelectTimezone')"
          />
        </VCol>
      </VRow>
      <!--
        <div class="mb-4 border pa-4 rounded">
        <div class="d-flex align-center justify-space-between">
        <div>
        <div class="text-h6 mb-2">启用NTP客户端</div>
        <div class="text-subtitle-1 mb-2" @click.stop.prevent>
        <span>启用后，设备将定期从NTP服务器同步时间</span>
        </div>
        </div>
        <div @click="changeFlag">
        <v-switch></v-switch>
        </div>
        </div>
        <VRow class="match-height mt-2" v-show="LanInfo.dhcpDisabled == '0'">
        <VCol cols="12" md="6">
        <div class="text-h6 mb-1">NTP服务器</div>
        <AppTextField v-model="LanInfo.dhcpStartValue" placeholder="请输入NTP服务器" />
        </VCol>
        <VCol cols="12" md="6">
        <div class="text-h6 mb-1">备用NTP服务器</div>
        <AppTextField v-model="LanInfo.dhcpMaxNumber" placeholder="请输入备用NTP服务器" />
        </VCol>
        </VRow>
        </div>
      -->
      <div
        class="mt-2"
        style="display: flex;justify-content: flex-end;"
      >
        <VBtn
          color="primary"
          @click="saveTimezone"
        >
          {{ t('SystemConfig.System.SaveSettings') }}
        </VBtn>
      </div>
    </VCardText>
  </VCard>
</template>

<style lang="scss" scoped>
.blueArea {
  p {
    font-family: "PingFang SC";
    font-feature-settings: "liga" off, "clig" off;
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;

    /* 160% */
    margin-block-end: 0;
  }

  .bold {
    font-family: "PingFang SC";
    font-feature-settings: "liga" off, "clig" off;
    font-size: 15px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }

  .text {
    position: relative;
    margin-block: 5px;
    margin-inline: 0;
  }

  .text::before {
    position: absolute;
    border-radius: 3px;
    background: var();
    block-size: 6px;
    content: "";
    inline-size: 6px;
    inset-block-start: 9px;
    inset-inline-start: 9px;
  }
}

.fontText {
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 13px;

  /* 100% */
}

.desc {
  /* 146.667% */
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--Light-Text-Secondary, text-secondary);

  /* Basic Typography/body-1 */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}

.flexBox {
  display: flex;
  align-items: center;

  .label {
    color: rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity));

    /* Basic Typography/h5 */
    font-family: "PingFang SC";
    font-feature-settings: "liga" off, "clig" off;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px;

    /* 155.556% */
  }

  .content {
    color: rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity));

    /* Basic Typography/h5 */
    font-family: "PingFang SC";
    font-feature-settings: "liga" off, "clig" off;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px;

    /* 155.556% */
  }

  .flex-1 {
    flex: 1;
  }

  .mr-12px {
    margin-inline-end: 12px !important;
  }
}
</style>
